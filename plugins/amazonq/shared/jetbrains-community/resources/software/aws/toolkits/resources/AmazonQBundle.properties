amazonqInlineChat.hint.edit = Edit
amazonqInlineChat.popup.accept=Accept \u23CE
amazonqInlineChat.popup.cancel=Cancel \u238B
amazonqInlineChat.popup.confirm=Confirm \u23CE
amazonqInlineChat.popup.editCode = Edit Code
amazonqInlineChat.popup.generating = Generating...
amazonqInlineChat.popup.reject=Reject \u238B
amazonqInlineChat.popup.title=Enter Instructions for Q
amazonq.refresh.panel=Refresh Chat Session
amazonq.title=Amazon Q
amazonq.workspace.settings.open.prompt=Workspace index is now enabled. You can disable it from Amazon Q settings.
action.q.profile.usage.text=You changed your profile
action.q.profile.usage=You''re using the ''<b>{0}</b>'' profile for Amazon Q.
action.q.switchProfiles.text=Change Profile
action.q.switchProfiles.text.action_required=<html><body>Change Profile <font color="{0}}">Select a profile to proceed</font></body></html>
action.q.switchProfiles.dialog.text=Amazon Q Developer Profile
action.q.switchProfiles.dialog.account.label=Account: {0}
action.q.switchProfiles.dialog.panel.text=Change your Q Developer profile
action.q.switchProfiles.dialog.panel.description=Choose the profile that meets your current working needs.
action.q.switchProfiles.dialog.panel.warning=<b>When you change profiles, you will no longer have access to your current customizations, chats, code reviews, or any other code or content being generated by Amazon Q.</b>
general.ok=OK
general.cancel=Cancel
